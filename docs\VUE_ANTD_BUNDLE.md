# Vue + Ant Design Standalone Bundle

This document describes the standalone Vue + Ant Design Vue bundle created for the Chrome extension project.

## Overview

The Vue + Ant Design bundle (`vue-antd-bundle.js`) is a separate JavaScript file that contains Vue.js 3, Pinia, and Ant Design Vue components. This bundle can be loaded independently from the main application code, providing several benefits:

- **Reduced main bundle size**: Vue and Ant Design are separated from application logic
- **Shared dependencies**: Multiple entry points can share the same Vue/Antd bundle
- **Content script compatibility**: Can be loaded in content scripts with CSP restrictions
- **Modular architecture**: Clear separation between framework code and application code

## Bundle Contents

### Vue 3 Core
- `createApp` - Create Vue application instances
- `ref`, `reactive` - Reactivity system
- `computed`, `watch` - Computed properties and watchers
- `onMounted`, `onUnmounted` - Lifecycle hooks
- `nextTick`, `isRef` - Utility functions

### Pinia State Management
- `createPinia` - Create Pinia store instances

### Ant Design Vue Components
- **Form Controls**: `AButton`, `AInput`, `AInputNumber`, `ASelect`, `ACheckbox`, `ARadio`
- **Layout**: `ARow`, `ACol`, `ALayout`, `ASpace`, `ADivider`
- **Navigation**: `AMenu`, `ATabs`, `ADropdown`
- **Data Display**: `ACard`, `AList`, `AAvatar`, `ABadge`, `ATag`, `ATooltip`
- **Feedback**: `AModal`, `ADrawer`, `AAlert`, `ASpin`, `AProgress`
- **Data Entry**: `AForm`, `AFormItem`, `AUpload`, `ASlider`, `ASwitch`
- **Other**: `APopover` and more

### Ant Design Icons
- **Directional**: `DownOutlined`, `UpOutlined`, `LeftOutlined`, `RightOutlined`
- **Actions**: `PlusOutlined`, `MinusOutlined`, `EditOutlined`, `DeleteOutlined`
- **General**: `SearchOutlined`, `SettingOutlined`, `CloseOutlined`, `CheckOutlined`
- **Status**: `ExclamationCircleOutlined`, `InfoCircleOutlined`, `LoadingOutlined`
- **Media**: `EyeOutlined`, `CopyOutlined`, `DownloadOutlined`, `UploadOutlined`

## Build Configuration

The bundle is configured in `vite.config.ts`:

```typescript
rollupOptions: {
  input: {
    // ... other entries
    'vue-antd-bundle': fileURLToPath(new URL('src/shared/vue-antd-bundle.ts', import.meta.url))
  }
}
```

## Usage Methods

### Method 1: Using the Loader Utility (Recommended)

```typescript
import { loadVueAntdBundle, createVueAppFromBundle } from '@/shared/vue-antd-loader'

// Create a Vue component
const MyComponent = {
  template: `
    <div>
      <a-button type="primary" @click="handleClick">
        Click me! Count: {{ count }}
      </a-button>
    </div>
  `,
  setup() {
    const bundle = await loadVueAntdBundle()
    const { ref } = bundle.Vue
    const count = ref(0)
    
    const handleClick = () => {
      count.value++
    }
    
    return { count, handleClick }
  }
}

// Create and mount the app
const app = await createVueAppFromBundle(MyComponent)
app.mount('#my-app')
```

### Method 2: Direct Global Access

```typescript
// Access the global bundle (after it's loaded)
if (window.VueAntdBundle) {
  const { Vue, AntdComponents, AntdIcons } = window.VueAntdBundle
  const { createApp, ref } = Vue
  const { AButton, AInput } = AntdComponents
  
  const app = createApp({
    components: { AButton, AInput },
    setup() {
      const message = ref('Hello!')
      return { message }
    },
    template: `
      <div>
        <a-input v-model:value="message" />
        <a-button type="primary">{{ message }}</a-button>
      </div>
    `
  })
  
  app.mount('#app')
}
```

### Method 3: Individual Function Access

```typescript
import { getVueReactivity, getAntdComponents } from '@/shared/vue-antd-loader'

// Get specific parts of the bundle
const { ref, reactive, computed } = await getVueReactivity()
const { AButton, AModal } = await getAntdComponents()

// Use them in your code
const count = ref(0)
const doubleCount = computed(() => count.value * 2)
```

## Content Script Usage

For Chrome extension content scripts:

```typescript
// content-script.ts
import { loadVueAntdBundle } from '@/shared/vue-antd-loader'

async function injectVueApp() {
  try {
    // Load the bundle
    const bundle = await loadVueAntdBundle()
    const { createApp, ref } = bundle.Vue
    const { AButton } = bundle.AntdComponents
    
    // Create container
    const container = document.createElement('div')
    container.id = 'my-extension-app'
    document.body.appendChild(container)
    
    // Create Vue app
    const app = createApp({
      components: { AButton },
      setup() {
        const count = ref(0)
        return { count }
      },
      template: `
        <div style="position: fixed; top: 10px; right: 10px; z-index: 9999;">
          <a-button type="primary" @click="count++">
            Clicks: {{ count }}
          </a-button>
        </div>
      `
    })
    
    app.mount('#my-extension-app')
  } catch (error) {
    console.error('Failed to inject Vue app:', error)
  }
}

// Inject when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', injectVueApp)
} else {
  injectVueApp()
}
```

## Manifest Configuration

The bundle is included in the Chrome extension manifest:

```json
{
  "web_accessible_resources": [
    {
      "resources": ["js/vue-antd-bundle.js"],
      "matches": ["<all_urls>"]
    }
  ]
}
```

## Bundle Size

- **Uncompressed**: ~372 KB
- **Gzipped**: ~109 KB

## Benefits

1. **Modularity**: Clear separation between framework and application code
2. **Reusability**: Can be shared across multiple entry points
3. **Performance**: Reduces duplication when multiple scripts need Vue/Antd
4. **CSP Compatibility**: Works in content scripts with strict CSP policies
5. **Development**: Easier to debug and maintain separate concerns

## Example Files

- `examples/vue-antd-bundle-usage.html` - Complete usage example
- `src/shared/vue-antd-bundle.ts` - Bundle source code
- `src/shared/vue-antd-loader.ts` - Loader utility functions

## TypeScript Support

The bundle includes full TypeScript definitions:

```typescript
import type { VueAntdBundleType } from '@/shared/vue-antd-bundle'

const bundle: VueAntdBundleType = await loadVueAntdBundle()
```

## Troubleshooting

### Bundle Not Loading
- Ensure the bundle is built: `npm run build`
- Check that `js/vue-antd-bundle.js` exists in the dist folder
- Verify manifest.json includes the bundle in web_accessible_resources

### CSP Issues
- The bundle is designed to work with Chrome extension CSP
- Use the loader utility for dynamic loading in content scripts

### Component Not Found
- Check that the component is included in the bundle
- Use the correct component name (e.g., `AButton`, not `Button`)

## Future Enhancements

- Add more Ant Design components as needed
- Optimize bundle size by tree-shaking unused components
- Add support for Ant Design themes
- Create specialized bundles for different use cases

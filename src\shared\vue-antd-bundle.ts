/**
 * Standalone Vue + Ant Design Vue bundle
 * This file creates a separate bundle containing Vue.js and Ant Design Vue
 * that can be loaded independently from the main application code.
 *
 * This is useful for:
 * - Reducing main bundle size
 * - Sharing Vue/Antd across multiple entry points
 * - Content script usage where CSP restrictions apply
 */

// Vue core
import {
  createApp,
  ref,
  reactive,
  computed,
  watch,
  onMounted,
  onUnmounted,
  nextTick,
  isRef
} from 'vue'
import type { App } from 'vue'

// Pinia for state management
import { createPinia } from 'pinia'

// Ant Design Vue components (commonly used ones)
import {
  Button as AButton,
  Input as AInput,
  InputNumber as AInputNumber,
  Select as ASelect,
  Dropdown as ADropdown,
  Menu as AMenu,
  <PERSON><PERSON> as AModal,
  Drawer as ADrawer,
  Tabs as ATabs,
  TabPane as ATabPane,
  Card as ACard,
  List as AList,
  ListItem as AListItem,
  Avatar as AAvatar,
  Badge as ABadge,
  Tag as ATag,
  Tooltip as ATooltip,
  <PERSON><PERSON> as APopover,
  <PERSON>lide<PERSON> as ASlider,
  Switch as ASwitch,
  Checkbox as ACheckbox,
  Radio as ARadio,
  RadioGroup as ARadioGroup,
  Form as AForm,
  FormItem as AFormItem,
  Upload as AUpload,
  Progress as AProgress,
  Spin as ASpin,
  Alert as AAlert,
  Divider as ADivider,
  Space as ASpace,
  Row as ARow,
  Col as ACol,
  Layout as ALayout,
  LayoutHeader as ALayoutHeader,
  LayoutContent as ALayoutContent,
  LayoutSider as ALayoutSider,
  LayoutFooter as ALayoutFooter
} from 'ant-design-vue'

// Ant Design Vue icons
import {
  DownOutlined,
  UpOutlined,
  LeftOutlined,
  RightOutlined,
  PlusOutlined,
  MinusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  SettingOutlined,
  CloseOutlined,
  CheckOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
  QuestionCircleOutlined,
  LoadingOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  CopyOutlined,
  DownloadOutlined,
  UploadOutlined
} from '@ant-design/icons-vue'

// Create global Vue + Ant Design bundle
const VueAntdBundle = {
  // Vue core
  Vue: {
    createApp,
    ref,
    reactive,
    computed,
    watch,
    onMounted,
    onUnmounted,
    nextTick,
    isRef
  },

  // Pinia
  Pinia: {
    createPinia
  },

  // Ant Design Vue components
  AntdComponents: {
    AButton,
    AInput,
    AInputNumber,
    ASelect,
    ADropdown,
    AMenu,
    AModal,
    ADrawer,
    ATabs,
    ATabPane,
    ACard,
    AList,
    AListItem,
    AAvatar,
    ABadge,
    ATag,
    ATooltip,
    APopover,
    ASlider,
    ASwitch,
    ACheckbox,
    ARadio,
    ARadioGroup,
    AForm,
    AFormItem,
    AUpload,
    AProgress,
    ASpin,
    AAlert,
    ADivider,
    ASpace,
    ARow,
    ACol,
    ALayout,
    ALayoutHeader,
    ALayoutContent,
    ALayoutSider,
    ALayoutFooter
  },

  // Ant Design Vue icons
  AntdIcons: {
    DownOutlined,
    UpOutlined,
    LeftOutlined,
    RightOutlined,
    PlusOutlined,
    MinusOutlined,
    EditOutlined,
    DeleteOutlined,
    SearchOutlined,
    SettingOutlined,
    CloseOutlined,
    CheckOutlined,
    ExclamationCircleOutlined,
    InfoCircleOutlined,
    QuestionCircleOutlined,
    LoadingOutlined,
    EyeOutlined,
    EyeInvisibleOutlined,
    CopyOutlined,
    DownloadOutlined,
    UploadOutlined
  }
}

// Make it available globally for Chrome extension context
if (typeof window !== 'undefined') {
  ;(window as any).VueAntdBundle = VueAntdBundle
}

// Also export for module usage
export default VueAntdBundle

// Export individual parts for convenience
export const { Vue, Pinia, AntdComponents, AntdIcons } = VueAntdBundle

// Type definitions for TypeScript support
export type VueAntdBundleType = typeof VueAntdBundle
export type VueType = typeof Vue
export type PiniaType = typeof Pinia
export type AntdComponentsType = typeof AntdComponents
export type AntdIconsType = typeof AntdIcons

// Helper function to create a Vue app with Ant Design components pre-registered
export function createVueAppWithAntd(rootComponent: any): App {
  const app = createApp(rootComponent)
  const pinia = createPinia()

  // Register Pinia
  app.use(pinia)

  // Register commonly used Ant Design components globally
  Object.entries(AntdComponents).forEach(([name, component]) => {
    app.component(name, component)
  })

  return app
}

// Helper function for content scripts to access the bundle
export function getVueAntdBundle(): VueAntdBundleType {
  if (typeof window !== 'undefined' && (window as any).VueAntdBundle) {
    return (window as any).VueAntdBundle
  }
  return VueAntdBundle
}

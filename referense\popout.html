<div id="d-menu-portals">
  <div
    class="fk-d-menu toolbar-menu__options-content toolbar-popup-menu-options -animated -expanded"
    data-identifier="toolbar-menu__options"
    data-content=""
    aria-labelledby="ember306"
    aria-expanded="true"
    role="dialog"
    style="max-width: 400px; left: 10px; top: 1360.84px; visibility: visible"
    data-strategy="absolute"
    data-placement="top"
  >
    <div class="fk-d-menu__inner-content">
      <ul class="dropdown-menu">
        <li class="dropdown-menu__item">
          <button class="btn btn-icon-text" title="引用整个帖子" data-name="quote" type="button">
            <svg
              class="fa d-icon d-icon-far-comment svg-icon svg-string"
              aria-hidden="true"
              xmlns="http://www.w3.org/2000/svg"
            >
              <use href="#far-comment"></use>
            </svg>
            <span class="d-button-label">
              <span class="d-button-label__text">引用整个帖子</span>
            </span>
          </button>
        </li>
        <li class="dropdown-menu__item">
          <button
            class="btn btn-icon-text"
            title="预先格式化的文本"
            data-name="format-code"
            type="button"
          >
            <svg
              class="fa d-icon d-icon-code svg-icon svg-string"
              aria-hidden="true"
              xmlns="http://www.w3.org/2000/svg"
            >
              <use href="#code"></use>
            </svg>
            <span class="d-button-label">
              <span class="d-button-label__text">预先格式化的文本</span>
            </span>
          </button>
        </li>
        <li class="dropdown-menu__item">
          <button
            class="btn btn-icon-text"
            title="项目符号列表"
            data-name="apply-unordered-list"
            type="button"
          >
            <svg
              class="fa d-icon d-icon-list-ul svg-icon svg-string"
              aria-hidden="true"
              xmlns="http://www.w3.org/2000/svg"
            >
              <use href="#list-ul"></use>
            </svg>
            <span class="d-button-label">
              <span class="d-button-label__text">项目符号列表</span>
            </span>
          </button>
        </li>
        <li class="dropdown-menu__item">
          <button
            class="btn btn-icon-text"
            title="编号列表"
            data-name="apply-ordered-list"
            type="button"
          >
            <svg
              class="fa d-icon d-icon-list-ol svg-icon svg-string"
              aria-hidden="true"
              xmlns="http://www.w3.org/2000/svg"
            >
              <use href="#list-ol"></use>
            </svg>
            <span class="d-button-label"><span class="d-button-label__text">编号列表</span></span>
          </button>
        </li>
        <li class="dropdown-menu__item">
          <button
            class="btn btn-icon-text"
            title="插入表"
            data-name="toggle-spreadsheet"
            type="button"
          >
            <svg
              class="fa d-icon d-icon-table svg-icon svg-string"
              aria-hidden="true"
              xmlns="http://www.w3.org/2000/svg"
            >
              <use href="#table"></use>
            </svg>
            <span class="d-button-label"><span class="d-button-label__text">插入表</span></span>
          </button>
        </li>
        <li class="dropdown-menu__item">
          <button class="btn btn-icon-text" title="Build Chart" type="button">
            <svg
              class="fa d-icon d-icon-chart-line svg-icon svg-string"
              aria-hidden="true"
              xmlns="http://www.w3.org/2000/svg"
            >
              <use href="#chart-line"></use>
            </svg>
            <span class="d-button-label">
              <span class="d-button-label__text">Build Chart</span>
            </span>
          </button>
        </li>
        <li class="dropdown-menu__item">
          <button class="btn btn-icon-text" title="隐藏详细信息" type="button">
            <svg
              class="fa d-icon d-icon-caret-right svg-icon svg-string"
              aria-hidden="true"
              xmlns="http://www.w3.org/2000/svg"
            >
              <use href="#caret-right"></use>
            </svg>
            <span class="d-button-label">
              <span class="d-button-label__text">隐藏详细信息</span>
            </span>
          </button>
        </li>
        <li class="dropdown-menu__item">
          <button
            class="btn btn-icon-text"
            title="插入日期/时间 (Ctrl ⇧ .)"
            data-name="local-dates"
            aria-keyshortcuts="Ctrl+⇧+."
            type="button"
          >
            <svg
              class="fa d-icon d-icon-far-clock svg-icon svg-string"
              aria-hidden="true"
              xmlns="http://www.w3.org/2000/svg"
            >
              <use href="#far-clock"></use>
            </svg>
            <span class="d-button-label">
              <span class="d-button-label__text">插入日期/时间</span>
              <kbd class="shortcut --always-visible">Ctrl ⇧ .</kbd>
            </span>
          </button>
        </li>
        <li class="dropdown-menu__item">
          <button class="btn btn-icon-text" title="Add footnote" type="button">
            <svg
              class="fa d-icon d-icon-asterisk svg-icon svg-string"
              aria-hidden="true"
              xmlns="http://www.w3.org/2000/svg"
            >
              <use href="#asterisk"></use>
            </svg>
            <span class="d-button-label">
              <span class="d-button-label__text">Add footnote</span>
            </span>
          </button>
        </li>
        <li class="dropdown-menu__item">
          <button class="btn btn-icon-text" title="构建投票" type="button">
            <svg
              class="fa d-icon d-icon-chart-bar svg-icon svg-string"
              aria-hidden="true"
              xmlns="http://www.w3.org/2000/svg"
            >
              <use href="#chart-bar"></use>
            </svg>
            <span class="d-button-label"><span class="d-button-label__text">构建投票</span></span>
          </button>
        </li>
        <li class="dropdown-menu__item">
          <button class="btn btn-icon-text" title="模糊剧透" type="button">
            <svg
              class="fa d-icon d-icon-wand-magic svg-icon svg-string"
              aria-hidden="true"
              xmlns="http://www.w3.org/2000/svg"
            >
              <use href="#wand-magic"></use>
            </svg>
            <span class="d-button-label">
              <span class="d-button-label__text">模糊剧透</span>
              <svg
                class="fa d-icon d-icon-check svg-icon d-button-label__active-icon svg-string"
                aria-hidden="true"
                xmlns="http://www.w3.org/2000/svg"
              >
                <use href="#check"></use>
              </svg>
            </span>
          </button>
        </li>
        <li class="dropdown-menu__item">
          <button class="btn btn-icon-text" title="插入滚动内容" type="button">
            <svg
              class="fa d-icon d-icon-file-invoice svg-icon svg-string"
              aria-hidden="true"
              xmlns="http://www.w3.org/2000/svg"
            >
              <use href="#file-invoice"></use>
            </svg>
            <span class="d-button-label">
              <span class="d-button-label__text">插入滚动内容</span>
            </span>
          </button>
        </li>
        <li class="dropdown-menu__item">
          <button class="btn btn-icon-text" title="Mermaid 图表" type="button">
            <svg
              class="fa d-icon d-icon-diagram-project svg-icon svg-string"
              aria-hidden="true"
              xmlns="http://www.w3.org/2000/svg"
            >
              <use href="#diagram-project"></use>
            </svg>
            <span class="d-button-label">
              <span class="d-button-label__text">Mermaid 图表</span>
            </span>
          </button>
        </li>
      </ul>
    </div>
  </div>
</div>

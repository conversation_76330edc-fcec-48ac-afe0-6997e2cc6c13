/**
 * Vue + Ant Design Bundle Loader
 * 
 * This utility helps load the standalone Vue + Ant Design bundle
 * in different contexts (content scripts, popup, options, etc.)
 */

import type { VueAntdBundleType } from './vue-antd-bundle'

// Global reference to the loaded bundle
let loadedBundle: VueAntdBundleType | null = null

/**
 * Load the Vue + Ant Design bundle dynamically
 * This is useful for content scripts that need to load the bundle on demand
 */
export async function loadVueAntdBundle(): Promise<VueAntdBundleType> {
  // Return cached bundle if already loaded
  if (loadedBundle) {
    return loadedBundle
  }

  // Check if bundle is already available globally (e.g., loaded by another script)
  if (typeof window !== 'undefined' && (window as any).VueAntdBundle) {
    loadedBundle = (window as any).VueAntdBundle
    return loadedBundle
  }

  // For Chrome extension context, load from extension resources
  if (typeof chrome !== 'undefined' && chrome.runtime) {
    try {
      // Get the extension URL for the bundle
      const bundleUrl = chrome.runtime.getURL('js/vue-antd-bundle.js')
      
      // Load the script dynamically
      await loadScript(bundleUrl)
      
      // Check if the bundle is now available
      if (typeof window !== 'undefined' && (window as any).VueAntdBundle) {
        loadedBundle = (window as any).VueAntdBundle
        return loadedBundle
      }
    } catch (error) {
      console.error('Failed to load Vue + Ant Design bundle from extension:', error)
    }
  }

  // Fallback: try to import the bundle directly (for build-time bundling)
  try {
    const bundleModule = await import('./vue-antd-bundle')
    loadedBundle = bundleModule.default
    return loadedBundle
  } catch (error) {
    console.error('Failed to import Vue + Ant Design bundle:', error)
    throw new Error('Could not load Vue + Ant Design bundle')
  }
}

/**
 * Load a script dynamically
 */
function loadScript(src: string): Promise<void> {
  return new Promise((resolve, reject) => {
    const script = document.createElement('script')
    script.src = src
    script.type = 'module'
    
    script.onload = () => {
      resolve()
    }
    
    script.onerror = () => {
      reject(new Error(`Failed to load script: ${src}`))
    }
    
    document.head.appendChild(script)
  })
}

/**
 * Check if the Vue + Ant Design bundle is available
 */
export function isVueAntdBundleAvailable(): boolean {
  return loadedBundle !== null || 
         (typeof window !== 'undefined' && (window as any).VueAntdBundle !== undefined)
}

/**
 * Get the Vue + Ant Design bundle synchronously
 * Returns null if not loaded yet
 */
export function getVueAntdBundleSync(): VueAntdBundleType | null {
  if (loadedBundle) {
    return loadedBundle
  }
  
  if (typeof window !== 'undefined' && (window as any).VueAntdBundle) {
    loadedBundle = (window as any).VueAntdBundle
    return loadedBundle
  }
  
  return null
}

/**
 * Create a Vue app using the loaded bundle
 * This is a convenience function that handles bundle loading and app creation
 */
export async function createVueAppFromBundle(rootComponent: any): Promise<any> {
  const bundle = await loadVueAntdBundle()
  return bundle.Vue.createApp(rootComponent)
}

/**
 * Get Vue reactive functions from the bundle
 */
export async function getVueReactivity() {
  const bundle = await loadVueAntdBundle()
  return {
    ref: bundle.Vue.ref,
    reactive: bundle.Vue.reactive,
    computed: bundle.Vue.computed,
    watch: bundle.Vue.watch,
    onMounted: bundle.Vue.onMounted,
    onUnmounted: bundle.Vue.onUnmounted,
    nextTick: bundle.Vue.nextTick,
    isRef: bundle.Vue.isRef
  }
}

/**
 * Get Ant Design components from the bundle
 */
export async function getAntdComponents() {
  const bundle = await loadVueAntdBundle()
  return bundle.AntdComponents
}

/**
 * Get Ant Design icons from the bundle
 */
export async function getAntdIcons() {
  const bundle = await loadVueAntdBundle()
  return bundle.AntdIcons
}

/**
 * Get Pinia from the bundle
 */
export async function getPinia() {
  const bundle = await loadVueAntdBundle()
  return bundle.Pinia
}

/**
 * Example usage for content scripts:
 * 
 * ```typescript
 * import { loadVueAntdBundle, createVueAppFromBundle } from '@/shared/vue-antd-loader'
 * 
 * // Load the bundle and create an app
 * const MyComponent = {
 *   template: '<div>Hello from Vue!</div>'
 * }
 * 
 * const app = await createVueAppFromBundle(MyComponent)
 * app.mount('#my-app')
 * ```
 * 
 * For accessing individual parts:
 * 
 * ```typescript
 * import { getVueReactivity, getAntdComponents } from '@/shared/vue-antd-loader'
 * 
 * const { ref, reactive } = await getVueReactivity()
 * const { AButton, AInput } = await getAntdComponents()
 * ```
 */
